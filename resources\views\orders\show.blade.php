@extends('layouts.admin')

@section('title', 'تفاصيل الطلب')

@section('styles')
<style>
    @page {
        margin: 20px 30px;
        size: A4;
    }

    body {
        font-family: 'DejaVu Sans', 'Arial Unicode MS', sans-serif;
        background-color: #f5f5f5;
        direction: rtl;
        text-align: right;
    }

    .a4-container {
        width: 210mm;
        min-height: auto;
        margin: 20px auto;
        background: white;
        box-shadow: 0 0 20px rgba(0,0,0,0.1);
        padding: 5mm;
        position: relative;
        border: 1px solid #ddd;
    }

    .action-buttons {
        background: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin-bottom: 20px;
        width: 100%;
        max-width: 250mm;
        margin-left: auto;
        margin-right: auto;
    }

    .invoice-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 10px;
        border-bottom: 2px solid #000;
        min-height: 50px;
    }

    .company-info {
        display: flex;
        align-items: center;
        gap: 5px;
        flex: 1;
    }

    .company-logo {
        width: 45px;
        height: 45px;
        object-fit: contain;
        flex-shrink: 0;
    }

    .company-text {
        text-align: right;
        flex: 1;
    }

    .company-name {
        font-size: 12px;
        font-weight: bold;
        color: #000;
        margin-bottom: 1mm;
        line-height: 1.1;
    }

    .company-subtitle {
        font-size: 9px;
        color: #666;
        margin-bottom: 0;
        line-height: 1.1;
    }

    .invoice-details {
        text-align: left;
        direction: ltr;
        min-width: 120px;
        flex-shrink: 0;
    }

    .invoice-number {
        font-size: 13px;
        font-weight: bold;
        color: #000;
        margin-bottom: 2px;
        line-height: 1.2;
    }

    .invoice-date {
        font-size: 10px;
        color: #666;
        line-height: 1.2;
    }

    .details-section {
        margin: 3mm 0;
    }

    .section-title {
        background: #f8f9fa;
        border: 1px solid #ddd;
        padding: 2mm 3mm;
        font-weight: bold;
        font-size: 9px;
        color: #333;
        margin-bottom: 0;
    }

    .details-table {
        width: 100%;
        border-collapse: collapse;
        border: 1px solid #ddd;
        font-size: 8px;
    }

    .details-table td {
        padding: 1mm 2mm;
        border: 1px solid #ddd;
        vertical-align: top;
    }

    .details-table .label-col {
        background: #f8f9fa;
        font-weight: bold;
        width: 25%;
        color: #333;
    }

    .details-table .value-col {
        width: 25%;
        color: #555;
    }

    .signature-section {
        margin-top: 5mm;
        display: flex;
        justify-content: space-between;
        align-items: flex-end;
    }

    .signature-box {
        text-align: center;
        width: 40mm;
    }

    .signature-line {
        border-bottom: 1px solid #000;
        height: 10mm;
        margin-bottom: 2mm;
    }

    .signature-label {
        font-size: 8px;
        font-weight: bold;
        color: #333;
    }

    .stamp-area {
        text-align: center;
        width: 25mm;
    }

    .stamp-circle {
        width: 20mm;
        height: 20mm;
        border: 1px dashed #ccc;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 2mm;
        font-size: 6px;
        color: #999;
    }

    .footer-info {
        margin-top: 20px;
        text-align: center;
        font-size: 9px;
        color: #666;
        border-top: 1px solid #ddd;
        padding-top: 10px;
    }

    @media print {
        body {
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
            margin: 0;
            padding: 0;
            background: white;
        }

        @page {
            margin: 20px 30px;
            size: A4;
        }

        .page-break {
            page-break-after: always;
        }

        html, body {
            width: 210mm;
            height: 297mm;
            margin: 0 !important;
            padding: 0 !important;
        }

        .wrapper {
            margin: 0 !important;
            padding: 0 !important;
        }

        .content {
            margin: 0 !important;
            padding: 0 !important;
        }

        * {
            box-sizing: border-box;
        }

        .action-buttons,
        .no-print,
        .pagination,
        .breadcrumb,
        .navbar,
        .footer-links,
        .back-button,
        .edit-button,
        .sidebar,
        .main-sidebar,
        .navbar-nav,
        .content-wrapper,
        .main-header,
        .main-footer,
        .control-sidebar,
        .navbar-expand,
        .navbar-light,
        .navbar-white,
        .layout-fixed,
        .layout-navbar-fixed,
        .layout-footer-fixed,
        .sidebar-mini,
        .nav-sidebar,
        .brand-link,
        .user-panel,
        .nav-header,
        .nav-item,
        .nav-link,
        .menu-open,
        .menu-is-opening,
        .has-treeview,
        .treeview-menu,
        .os-scrollbar,
        .os-scrollbar-horizontal,
        .os-scrollbar-vertical {
            display: none !important;
        }

        .a4-container {
            page-break-inside: avoid;
            box-shadow: none;
            margin: 0;
            width: 100%;
            min-height: auto;
            border: none;
            padding: 0;
        }

        table, tr, td, th {
            page-break-inside: avoid !important;
            break-inside: avoid !important;
        }

        .footer-info {
            position: fixed;
            bottom: 10px;
            left: 0;
            right: 0;
            text-align: center;
            font-size: 6px;
        }

        /* إخفاء جميع عناصر AdminLTE */
        body.sidebar-mini .main-sidebar,
        body.sidebar-mini-md .main-sidebar,
        body.sidebar-mini-xs .main-sidebar,
        .main-sidebar,
        .navbar,
        .main-header,
        .main-footer,
        .content-header,
        .breadcrumb,
        .card-header,
        .btn,
        button,
        .alert,
        .modal,
        .dropdown,
        .nav,
        .navbar-nav,
        .sidebar-dark-primary,
        .elevation-4,
        .brand-link,
        .user-panel,
        .sidebar,
        .os-host,
        .os-padding,
        .os-viewport,
        .os-content,
        .nav-pills,
        .nav-sidebar,
        .nav-flat,
        .nav-legacy,
        .nav-compact,
        .nav-child-indent,
        .layout-fixed .main-sidebar,
        .layout-navbar-fixed .main-header,
        .layout-footer-fixed .main-footer {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
        }

        /* ضمان عرض المحتوى الرئيسي فقط */
        .a4-container {
            position: relative !important;
            left: 0 !important;
            top: 0 !important;
            margin: 0 auto !important;
            width: 100% !important;
            max-width: 210mm !important;
        }
    }

    /* إخفاء عناصر إضافية في الطباعة */
    @media print {
        .wrapper {
            margin-left: 0 !important;
        }

        .content-wrapper {
            margin-left: 0 !important;
            padding: 0 !important;
        }

        body.sidebar-mini .content-wrapper,
        body.sidebar-mini-md .content-wrapper,
        body.sidebar-mini-xs .content-wrapper {
            margin-left: 0 !important;
        }
    }

    @media screen and (max-width: 768px) {
        .a4-container {
            width: 100%;
            margin: 10px;
            padding: 15px;
        }

        .invoice-header {
            flex-direction: column;
            text-align: center;
        }

        .company-info {
            justify-content: center;
            gap: 5px;
        }

        .company-logo {
            width: 35px;
            height: 35px;
        }

        .invoice-details {
            text-align: center;
            direction: rtl;
            margin-top: 10px;
        }

        .details-table .label-col,
        .details-table .value-col {
            width: 50%;
        }

        .signature-section {
            flex-direction: column;
            gap: 20px;
        }
    }
</style>
@endsection

@section('content')
<!-- Action Buttons (Outside A4 Container) -->
<div class="action-buttons">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h4 class="mb-0">تفاصيل الطلب: {{ $order->branch_serial ?? $order->order_number ?? '#' . $order->id }}</h4>
            <small class="text-muted">تم الإنشاء في: {{ $order->created_at->format('Y-m-d H:i') }}</small>
        </div>
        <div class="d-flex gap-2">
            <a href="{{ route('orders.index') }}" class="btn btn-secondary">
                <i class="fas fa-arrow-right me-1"></i> العودة للقائمة
            </a>
            <a href="{{ route('orders.edit', $order) }}" class="btn btn-warning">
                <i class="fas fa-edit me-1"></i> تعديل
            </a>
            @if(Route::has('orders.pdf'))
            <a href="{{ route('orders.pdf', $order) }}" class="btn btn-success">
                <i class="fas fa-file-pdf me-1"></i> تحميل PDF (TCPDF)
            </a>
            @endif
            <button class="btn btn-primary" onclick="window.print()">
                <i class="fas fa-print me-1"></i> طباعة
            </button>
        </div>
    </div>
</div>

<!-- A4 Document Container -->
<div class="a4-container">
    <!-- Invoice Header -->
    <div class="invoice-header">
        <div class="company-info">
            <img src="{{ asset('images/logos/aswsd.png') }}" alt="شعار الشركة" class="company-logo">
            <div class="company-text">
                <div class="company-name">مجموعة أبراهيم الاحمدي اليمنية</div>
                <div class="company-subtitle">للتجارة والخدمات اللوجستية</div>
            </div>
        </div>
        <div class="invoice-details">
            <div class="invoice-number">Order #{{ $order->branch_serial ?? $order->order_number ?? $order->id }}</div>
            <div class="invoice-date">{{ $order->created_at->format('Y/m/d') }}</div>
        </div>
    </div>

    <!-- Order Details Section -->
    <div class="details-section">
        <div class="section-title">معلومات الطلب</div>
        <table class="details-table">
            <tr>
                <td class="label-col">رقم الطلب</td>
                <td class="value-col">{{ $order->branch_serial ?? $order->order_number ?? $order->id }}</td>
                <td class="label-col">تاريخ الطلب</td>
                <td class="value-col">{{ $order->request_date ?? $order->created_at->format('Y-m-d') }}</td>
            </tr>
            <tr>
                <td class="label-col">اسم العميل</td>
                <td class="value-col">{{ $order->customer_name ?? 'غير محدد' }}</td>
                <td class="label-col">رقم الهاتف</td>
                <td class="value-col">{{ $order->phone_number ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">اسم المستلم</td>
                <td class="value-col">{{ $order->recipient_name ?? 'غير محدد' }}</td>
                <td class="label-col">نوع الخدمة</td>
                <td class="value-col">{{ $order->service_type ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">حالة الطلب</td>
                <td class="value-col">
                    @switch($order->status)
                        @case('تم الاتفاق')
                            <span style="color: #28a745; font-weight: bold; background: #d4edda; padding: 4px 8px; border-radius: 4px; border: 1px solid #c3e6cb;">
                                ✓ تم الاتفاق
                            </span>
                            @break
                        @case('قيد المتابعة')
                            <span style="color: #856404; font-weight: bold; background: #fff3cd; padding: 4px 8px; border-radius: 4px; border: 1px solid #ffeaa7;">
                                ⏳ قيد المتابعة
                            </span>
                            @break
                        @case('ملغي')
                            <span style="color: #721c24; font-weight: bold; background: #f8d7da; padding: 4px 8px; border-radius: 4px; border: 1px solid #f5c6cb;">
                                ✗ ملغي
                            </span>
                            @break
                        @default
                            <span style="color: #6c757d; font-weight: bold; background: #e9ecef; padding: 4px 8px; border-radius: 4px; border: 1px solid #ced4da;">
                                {{ $order->status ?? 'غير محدد' }}
                            </span>
                    @endswitch
                </td>
                <td class="label-col">المسؤول</td>
                <td class="value-col">{{ $order->user_name ?? ($order->user->name ?? 'غير محدد') }}</td>
            </tr>
        </table>
    </div>

    <!-- Goods Details Section -->
    <div class="details-section">
        <div class="section-title">تفاصيل البضاعة</div>
        <table class="details-table">
            <tr>
                <td class="label-col">نوع البضاعة</td>
                <td class="value-col">{{ $order->goods_name ?? 'غير محدد' }}</td>
                <td class="label-col">بلد المنشأ</td>
                <td class="value-col">{{ $order->country_of_origin ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">الوزن</td>
                <td class="value-col">{{ $order->weight ?? 'غير محدد' }}</td>
                <td class="label-col">الكمية</td>
                <td class="value-col">{{ $order->quantity ?? 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">منطقة المغادرة</td>
                <td class="value-col">{{ $order->departure_area ?? 'غير محدد' }}</td>
                <td class="label-col">منطقة التسليم</td>
                <td class="value-col">{{ $order->delivery_area ?? 'غير محدد' }}</td>
            </tr>
        </table>
    </div>

    <!-- Financial Details Section -->
    <div class="details-section">
        <div class="section-title">التفاصيل المالية</div>
        <table class="details-table">
            @php
                $currencyDisplay = match($order->currency ?? 'ريال') {
                    'ريال' => 'ريال يمني',
                    'دولار' => 'دولار أمريكي',
                    'ريال سعودي' => 'ريال سعودي',
                    'درهم' => 'درهم إماراتي',
                    default => $order->currency ?? 'ريال يمني'
                };
            @endphp
            <tr>
                <td class="label-col">رسوم الخدمة</td>
                <td class="value-col">{{ $order->service_fees ? number_format($order->service_fees, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
                <td class="label-col">المبلغ المدفوع</td>
                <td class="value-col">{{ $order->paid_amount ? number_format($order->paid_amount, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">مبلغ العميل المتفق عليه</td>
                <td class="value-col">{{ $order->customer_agreed_amount ? number_format($order->customer_agreed_amount, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
                <td class="label-col">المبلغ المتبقي</td>
                <td class="value-col">{{ $order->remaining_amount ? number_format($order->remaining_amount, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">مبلغ الوكيل المتفق عليه</td>
                <td class="value-col">{{ $order->agent_agreed_amount ? number_format($order->agent_agreed_amount, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
                <td class="label-col">مصاريف أخرى</td>
                <td class="value-col">{{ $order->other_expenses ? number_format($order->other_expenses, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
            </tr>
            <tr>
                <td class="label-col">العملة</td>
                <td class="value-col">{{ $currencyDisplay }}</td>
                <td class="label-col" style="background: #e8f5e8; font-weight: bold;">صافي الربح</td>
                <td class="value-col" style="background: #e8f5e8; font-weight: bold; color: #27ae60;">{{ $order->profit ? number_format($order->profit, 2) . ' ' . $currencyDisplay : 'غير محدد' }}</td>
            </tr>
        </table>
    </div>

    <!-- Notes Section -->
    @if($order->notes)
    <div class="details-section">
        <div class="section-title">ملاحظات</div>
        <div style="padding: 15px; border: 1px solid #ddd; border-top: none; min-height: 60px;">
            {{ $order->notes }}
        </div>
    </div>
    @endif

    <!-- Signature Section -->
    <div class="signature-section">
        <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-label">توقيع العميل</div>
        </div>

        <div class="stamp-area">
            <div class="stamp-circle">
                ختم الشركة
            </div>
            <div class="signature-label">ختم الشركة</div>
        </div>

        <div class="signature-box">
            <div class="signature-line"></div>
            <div class="signature-label">توقيع المسؤول</div>
            <div style="font-size: 10px; color: #666; margin-top: 5px;">
                {{ $order->user_name ?? ($order->user->name ?? 'غير محدد') }}
            </div>
        </div>
    </div>

    <!-- Footer Information -->
    <div class="footer-info">
        <div>مجموعة أبراهيم الاحمدي اليمنية للتجارة والخدمات اللوجستية</div>
        <div>الهاتف: 738504800 | البريد الإلكتروني: <EMAIL></div>
        <div>العنوان: اليمن - حضرموت - المكلا</div>
        <div style="margin-top: 10px; font-size: 9px;">
            تم إنشاء هذا المستند في: {{ now()->format('Y-m-d H:i:s') }}
        </div>
    </div>
</div>

<script>
function printOrder() {
    window.print();
}
</script>
@endsection